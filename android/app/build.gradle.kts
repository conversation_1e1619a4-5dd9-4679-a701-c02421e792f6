// android/app/build.gradle.kts  — multi‑ABI for debug, arm64‑only for release
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.eddars.rockpaperscissors"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions { jvmTarget = "11" }

    defaultConfig {
        applicationId = "com.eddars.rockpaperscissors"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"
    }

    buildTypes {
        debug {
            // Réduire les ABIs pour debug pour économiser la mémoire
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "x86_64") // Seulement les ABIs essentiels
            }
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
        }
        release {
            // keep APK small for release on physical devices
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a")
            }
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = true  // Activer la minification pour release
            isShrinkResources = true // Activer la réduction des ressources
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    packaging {
        jniLibs {
            // ensure .so are extracted on older devices and for JNI loader
            useLegacyPackaging = true
        }
        resources {
            pickFirsts += listOf(
                "META-INF/LICENSE.md",
                "META-INF/LICENSE-notice.md"
            )
        }
    }

    // do NOT compress mediapipe model files
    androidResources {
        noCompress += setOf("tflite", "lite", "bin", "task")
    }
}

flutter { source = "../.." }

dependencies {
    // Dépendances de base pour la caméra
    implementation("androidx.camera:camera-core:1.3.4")
    implementation("androidx.camera:camera-camera2:1.3.4")
    implementation("androidx.camera:camera-lifecycle:1.3.4")
    implementation("androidx.camera:camera-view:1.3.4")

    // MediaPipe pour la reconnaissance de gestes - version très stable
    implementation("com.google.mediapipe:tasks-vision:0.10.8")
    implementation("com.google.guava:guava:31.1-android")
}
