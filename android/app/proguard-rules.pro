# Règles ProGuard pour MediaPipe et Rock Paper Scissors

# Garder toutes les classes MediaPipe
-keep class com.google.mediapipe.** { *; }
-dontwarn com.google.mediapipe.**

# Garder les classes de gestion des gestes
-keep class com.eddars.rockpaperscissors.gesture.** { *; }

# Garder les classes Flutter
-keep class io.flutter.** { *; }
-dontwarn io.flutter.**

# Garder les classes Camera
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Garder les classes Guava utilisées par MediaPipe
-keep class com.google.common.** { *; }
-dontwarn com.google.common.**

# Garder les annotations
-keepattributes *Annotation*

# Garder les classes natives
-keepclasseswithmembernames class * {
    native <methods>;
}

# Optimisations générales
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose
