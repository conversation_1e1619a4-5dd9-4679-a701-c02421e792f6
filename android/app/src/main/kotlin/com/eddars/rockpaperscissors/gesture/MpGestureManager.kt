package com.eddars.rockpaperscissors.gesture

import android.Manifest
import android.content.pm.PackageManager
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.EventChannel

// Imports MediaPipe - seront gérés avec try-catch à l'exécution
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.framework.image.MediaImageBuilder
import com.google.mediapipe.tasks.vision.core.ImageProcessingOptions
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizer
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizerResult
import io.flutter.FlutterInjector

class MpGestureManager(private val activity: FlutterActivity) : EventChannel.StreamHandler {

    private var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>? = null
    private var analysis: ImageAnalysis? = null
    private var recognizer: GestureRecognizer? = null
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.65

    fun start() {
        ensurePermissionAndStart()
    }

    fun setMinScore(minScore: Double) {
        this.minScore = minScore
    }

    fun stop() {
        try {
            val provider = cameraProviderFuture?.get()
            provider?.unbindAll()
        } catch (_: Throwable) {}
        analysis = null
        recognizer?.close()
        recognizer = null
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
        sink = events
    }

    override fun onCancel(arguments: Any?) {
        sink = null
    }

    fun onRequestPermissionsResult(requestCode: Int, grantResults: IntArray) {
        if (requestCode == 9001 && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startCameraAndRecognizer()
        }
    }

    private fun ensurePermissionAndStart() {
        val granted = ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        if (!granted) {
            ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.CAMERA), 9001)
            return
        }
        startCameraAndRecognizer()
    }

    private fun startCameraAndRecognizer() {
        if (recognizer == null) {
            val modelPath = ensureModelFile("assets/gesture_recognizer.task")
            android.util.Log.d("MpGestureManager", "Chargement du modèle depuis: $modelPath")

            // Configuration optimisée pour la détection automatique de main
            val options = GestureRecognizer.GestureRecognizerOptions.builder()
                .setBaseOptions(
                    BaseOptions.builder()
                        .setModelAssetPath(modelPath)
                        .build()
                )
                .setMinHandDetectionConfidence(0.5f)      // Seuil de détection de main
                .setMinHandPresenceConfidence(0.5f)       // Seuil de présence de main
                .setMinTrackingConfidence(0.3f)           // Seuil de suivi
                .setNumHands(1)                           // Détecter une seule main (la plus visible)
                .setRunningMode(RunningMode.LIVE_STREAM)   // Mode temps réel
                .setResultListener { result, _ -> handleResult(result) }
                .build()

            recognizer = GestureRecognizer.createFromOptions(activity, options)
            android.util.Log.d("MpGestureManager", "✅ Modèle MediaPipe configuré pour détection automatique de main")
        }

        cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
        cameraProviderFuture?.addListener({
            val provider = cameraProviderFuture?.get() ?: return@addListener
            provider.unbindAll()

            // Configuration améliorée pour la caméra frontale
            val analyzer = ImageAnalysis.Builder()
                .setTargetResolution(Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                .build()

            analyzer.setAnalyzer(activity.mainExecutor) { imageProxy ->
                analyzeFrame(imageProxy)
            }

            // Vérifier la disponibilité de la caméra frontale
            val cameraSelector = try {
                if (provider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
                    android.util.Log.d("MpGestureManager", "✅ Caméra frontale disponible")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                        .build()
                } else {
                    android.util.Log.w("MpGestureManager", "⚠️ Caméra frontale non disponible, utilisation de la caméra arrière")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                }
            } catch (e: Exception) {
                android.util.Log.e("MpGestureManager", "❌ Erreur lors de la vérification de la caméra: ${e.message}")
                CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()
            }

            analysis = analyzer

            try {
                provider.bindToLifecycle(activity, cameraSelector, analyzer)
                android.util.Log.d("MpGestureManager", "✅ Caméra liée avec succès")
            } catch (e: Exception) {
                android.util.Log.e("MpGestureManager", "❌ Erreur lors de la liaison de la caméra: ${e.message}")
            }
        }, activity.mainExecutor)
    }

    private fun analyzeFrame(image: ImageProxy) {
        try {
            val mediaImage = image.image ?: run { image.close(); return }
            val mpImage: MPImage = MediaImageBuilder(mediaImage).build()

            // Pour la caméra frontale, nous devons ajuster la rotation
            // La caméra frontale est généralement miroir et nécessite une rotation différente
            var rotationDegrees = image.imageInfo.rotationDegrees

            // Ajustement spécifique pour la caméra frontale
            // La caméra frontale nécessite souvent une rotation supplémentaire
            rotationDegrees = when (rotationDegrees) {
                0 -> 270  // Portrait normal -> rotation pour caméra frontale
                90 -> 0   // Paysage gauche
                180 -> 90 // Portrait inversé
                270 -> 180 // Paysage droit
                else -> rotationDegrees
            }

            val options = ImageProcessingOptions.builder()
                .setRotationDegrees(rotationDegrees)
                .build()

            // Utiliser recognizeAsync pour le mode LIVE_STREAM
            val timestampMs = System.currentTimeMillis()
            recognizer?.recognizeAsync(mpImage, options, timestampMs)

        } catch (e: Throwable) {
            // Log l'erreur pour debug
            android.util.Log.e("MpGestureManager", "Erreur lors de l'analyse de frame: ${e.message}")
        } finally {
            image.close()
        }
    }

    private fun handleResult(result: GestureRecognizerResult?) {
        if (result == null) {
            android.util.Log.d("MpGestureManager", "Résultat null reçu")
            return
        }

        // Vérifier s'il y a des mains détectées
        val handLandmarks = result.landmarks()
        val handedness = result.handedness()

        if (handLandmarks.isNotEmpty() && handedness.isNotEmpty()) {
            // Détection automatique de la main (gauche ou droite)
            val detectedHand = handedness[0][0].categoryName() // "Left" ou "Right"
            val handConfidence = handedness[0][0].score()

            android.util.Log.d("MpGestureManager", "🖐️ Main détectée: $detectedHand (confiance: $handConfidence)")
        }

        val gestures = result.gestures() ?: run {
            android.util.Log.d("MpGestureManager", "Aucun geste détecté")
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }

        if (gestures.isEmpty()) {
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }

        val firstList = gestures[0] ?: run {
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }

        if (firstList.isEmpty()) {
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }

        val top = firstList[0]
        val label = if (top.score() >= minScore) top.categoryName() else "none"

        // Log détaillé pour debug
        val detectedHand = if (handedness.isNotEmpty()) handedness[0][0].categoryName() else "Unknown"
        android.util.Log.d("MpGestureManager", "🎯 Geste détecté: ${top.categoryName()} avec score: ${top.score()}")
        android.util.Log.d("MpGestureManager", "🖐️ Main utilisée: $detectedHand")
        android.util.Log.d("MpGestureManager", "⚖️ Seuil minimum: $minScore, Label final: $label")

        // Envoyer le résultat avec information sur la main
        sink?.success(mapOf(
            "label" to label,
            "score" to top.score().toDouble(),
            "hand" to detectedHand,
            "ts" to System.currentTimeMillis()
        ))
    }

    // Copy Flutter asset to a real file path, so createFromFile can read it.
    private fun ensureModelFile(assetPath: String): String {
        val loader = FlutterInjector.instance().flutterLoader()
        val key = loader.getLookupKeyForAsset(assetPath)
        val outFile = java.io.File(activity.filesDir, "gesture_recognizer.task")
        if (outFile.exists() && outFile.length() > 0) return outFile.absolutePath

        activity.assets.open(key).use { input ->
            outFile.outputStream().use { output ->
                input.copyTo(output)
            }
        }
        return outFile.absolutePath
    }
}
