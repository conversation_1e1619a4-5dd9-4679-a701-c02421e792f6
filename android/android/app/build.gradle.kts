// android/app/build.gradle.kts  — multi‑ABI for debug, arm64‑only for release
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.eddars.rockpaperscissors"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions { jvmTarget = "11" }

    defaultConfig {
        applicationId = "com.eddars.rockpaperscissors"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"
    }

    buildTypes {
        debug {
            // include ALL ABIs in debug so emulator + device both work
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64", "x86")
            }
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
        }
        release {
            // keep APK small for release on physical devices
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a")
            }
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }

    packaging {
        jniLibs {
            // ensure .so are extracted on older devices and for JNI loader
            useLegacyPackaging = true
        }
        resources {
            pickFirsts += listOf(
                "META-INF/LICENSE.md",
                "META-INF/LICENSE-notice.md"
            )
        }
    }

    // do NOT compress mediapipe model files
    androidResources {
        noCompress += setOf("tflite", "lite", "bin", "task")
    }
}

flutter { source = "../.." }

dependencies {
    implementation("com.google.mediapipe:tasks-vision:0.10.18")
    implementation("androidx.camera:camera-core:1.3.4")
    implementation("androidx.camera:camera-camera2:1.3.4")
    implementation("androidx.camera:camera-lifecycle:1.3.4")
    implementation("androidx.camera:camera-view:1.3.4")
    implementation("com.google.guava:guava:31.1-android")
}
