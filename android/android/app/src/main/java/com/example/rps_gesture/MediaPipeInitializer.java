package com.eddars.rps_gesture;

import android.content.Context;
import android.util.Log;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * MediaPipe Tasks Vision Initializer
 * Handles native library loading and initialization for MediaPipe Tasks
 */
public class MediaPipeInitializer {
    private static final String TAG = "MediaPipeInitializer";
    private static boolean isInitialized = false;
    private static boolean libraryLoaded = false;

    // Removed static initializer to prevent app crashes when libraries fail to load

    /**
     * Initialize MediaPipe native libraries (safe version)
     */
    private static void initializeLibraries(Context context) {
        if (libraryLoaded) {
            return;
        }

        Log.d(TAG, "🚀 Initializing MediaPipe Tasks Vision libraries (safe mode)...");

        try {
            // Load dependency libraries first (safe mode - don't fail if not available)
            loadDependencyLibraries();
            
            // Load main MediaPipe Tasks Vision library (safe mode)
            libraryLoaded = loadMediaPipeLibrary(context);
            
            if (libraryLoaded) {
                Log.d(TAG, "✅ MediaPipe libraries loaded successfully");
            } else {
                Log.w(TAG, "⚠️ MediaPipe libraries not available - will use fallback mode");
            }
            
        } catch (Exception e) {
            Log.w(TAG, "⚠️ MediaPipe library initialization failed (safe mode): " + e.getMessage());
            libraryLoaded = false;
        }
    }

    /**
     * Load dependency libraries in correct order
     */
    private static void loadDependencyLibraries() {
        // Load C++ runtime if available
        try {
            System.loadLibrary("c++_shared");
            Log.d(TAG, "✅ Loaded c++_shared");
        } catch (UnsatisfiedLinkError e) {
            Log.d(TAG, "⚠️ c++_shared not available or already loaded: " + e.getMessage());
        }

        // Load TensorFlow Lite if available
        try {
            System.loadLibrary("tensorflowlite_jni");
            Log.d(TAG, "✅ Loaded tensorflowlite_jni");
        } catch (UnsatisfiedLinkError e) {
            Log.d(TAG, "⚠️ tensorflowlite_jni not available: " + e.getMessage());
        }

        // Load MediaPipe core if available
        try {
            System.loadLibrary("mediapipe_jni");
            Log.d(TAG, "✅ Loaded mediapipe_jni");
        } catch (UnsatisfiedLinkError e) {
            Log.d(TAG, "⚠️ mediapipe_jni not available: " + e.getMessage());
        }
    }

    /**
     * Load main MediaPipe Tasks Vision library with multiple strategies
     */
    private static boolean loadMediaPipeLibrary(Context context) {
        try {
            // Strategy 1: Standard loading
            try {
                System.loadLibrary("mediapipe_tasks_vision_jni");
                Log.d(TAG, "✅ MediaPipe Tasks Vision JNI loaded via standard method");
                return true;
            } catch (UnsatisfiedLinkError e) {
                Log.w(TAG, "⚠️ Standard loading failed: " + e.getMessage());
            }

            // Strategy 2: Architecture-specific loading with intelligent fallback
            String abi = android.os.Build.SUPPORTED_ABIS[0];
            Log.d(TAG, "🔍 Device ABI: " + abi);
            
            String libName = "mediapipe_tasks_vision_jni";
            switch (abi) {
                case "arm64-v8a":
                case "armeabi-v7a":
                case "x86":
                    // Standard loading for supported architectures
                    try {
                        System.loadLibrary(libName);
                        Log.d(TAG, "✅ MediaPipe loaded for ABI: " + abi);
                        return true;
                    } catch (UnsatisfiedLinkError e) {
                        Log.w(TAG, "⚠️ Standard loading failed for " + abi + ": " + e.getMessage());
                        // Try manual extraction for this ABI
                        if (context != null) {
                            return extractLibraryForAbi(context, abi);
                        }
                    }
                    break;
                    
                case "x86_64":
                    // Special case: x86_64 with x86 fallback
                    Log.d(TAG, "🔄 x86_64 detected - trying with x86 fallback support");
                    
                    // First try standard x86_64 loading
                    try {
                        System.loadLibrary(libName);
                        Log.d(TAG, "✅ MediaPipe loaded for x86_64 (native)");
                        return true;
                    } catch (UnsatisfiedLinkError fallbackError) {
                        Log.w(TAG, "⚠️ x86_64 native library not found: " + fallbackError.getMessage());
                        
                        // Try manual extraction with intelligent fallback
                        if (context != null) {
                            Log.d(TAG, "🔄 Attempting x86_64 → x86 compatibility fallback...");
                            return tryManualExtractionWithFallback(context, "x86_64", "x86");
                        } else {
                            Log.w(TAG, "⚠️ No context available for manual extraction");
                        }
                    }
                    break;
                    
                default:
                    Log.w(TAG, "⚠️ Unsupported ABI: " + abi);
                    break;
            }

            Log.w(TAG, "⚠️ All loading strategies failed for ABI: " + abi);
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Unexpected error during library loading: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Try manual extraction with intelligent fallback (x86_64 → x86)
     */
    private static boolean tryManualExtractionWithFallback(Context context, String primaryAbi, String fallbackAbi) {
        if (context == null) {
            Log.w(TAG, "Context is null, cannot extract library");
            return false;
        }
        
        Log.d(TAG, "🔄 Attempting manual extraction with fallback: " + primaryAbi + " → " + fallbackAbi);
        
        // Try primary ABI first
        if (extractLibraryForAbi(context, primaryAbi)) {
            return true;
        }
        
        // Fallback to compatible ABI
        Log.d(TAG, "🔄 Primary ABI failed, trying fallback: " + fallbackAbi);
        return extractLibraryForAbi(context, fallbackAbi);
    }
    
    /**
     * Extract library for specific ABI
     */
    private static boolean extractLibraryForAbi(Context context, String abi) {
        try {
            String libName = "libmediapipe_tasks_vision_jni.so";
            String libPath = "lib/" + abi + "/" + libName;
            
            // Get APK path
            String apkPath = context.getApplicationInfo().sourceDir;
            Log.d(TAG, "📦 Checking for library: " + libPath + " in " + apkPath);
            
            // Extract library from APK
            try (ZipFile zipFile = new ZipFile(apkPath)) {
                ZipEntry entry = zipFile.getEntry(libPath);
                if (entry == null) {
                    Log.w(TAG, "⚠️ Library not found for ABI " + abi + ": " + libPath);
                    return false;
                }
                
                // Create temporary file
                File libDir = new File(context.getFilesDir(), "native_libs");
                if (!libDir.exists()) {
                    libDir.mkdirs();
                }
                
                File libFile = new File(libDir, libName);
                
                // Extract library
                try (InputStream inputStream = zipFile.getInputStream(entry);
                     FileOutputStream outputStream = new FileOutputStream(libFile)) {
                    
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }
                
                // Load extracted library with architecture compatibility check
                try {
                    System.load(libFile.getAbsolutePath());
                    Log.d(TAG, "✅ MediaPipe library loaded via manual extraction (" + abi + ")");
                    return true;
                } catch (UnsatisfiedLinkError archError) {
                    // Handle architecture incompatibility (e.g., 32-bit lib on 64-bit emulator)
                    if (archError.getMessage() != null && 
                        (archError.getMessage().contains("32-bit instead of 64-bit") ||
                         archError.getMessage().contains("64-bit instead of 32-bit"))) {
                        Log.w(TAG, "⚠️ Architecture incompatibility for " + abi + ": " + archError.getMessage());
                        Log.w(TAG, "💡 This is expected on x86_64 emulator with x86 libraries");
                        return false; // Return false to allow other fallback strategies
                    } else {
                        // Re-throw other types of UnsatisfiedLinkError
                        throw archError;
                    }
                }
                
            }
            
        } catch (Exception e) {
            Log.w(TAG, "⚠️ Manual library extraction failed for " + abi + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Check if MediaPipe is available and initialized
     */
    public static boolean isAvailable() {
        return isInitialized && libraryLoaded;
    }

    /**
     * CRITICAL: Check if current device/emulator has x86_64 MediaPipe compatibility issues
     */
    public static boolean hasX86_64Issues() {
        String abi = android.os.Build.SUPPORTED_ABIS[0];
        boolean isX86_64 = "x86_64".equals(abi);
        
        if (isX86_64) {
            Log.w(TAG, "⚠️ X86_64 DETECTED: This architecture commonly has MediaPipe compatibility issues");
            Log.w(TAG, "💡 KNOWN ISSUE: MediaPipe native libraries may not work on x86_64 emulators");
            Log.w(TAG, "💡 SOLUTION: Use arm64-v8a device/emulator or implement camera fallback");
            return true;
        }
        return false;
    }

    /**
     * Get comprehensive diagnostic information for x86_64 issues
     */
    public static String getX86_64Diagnostics() {
        StringBuilder diagnostics = new StringBuilder();
        String abi = android.os.Build.SUPPORTED_ABIS[0];
        
        diagnostics.append("🔍 X86_64 MediaPipe Diagnostics:\n");
        diagnostics.append("- Current ABI: ").append(abi).append("\n");
        diagnostics.append("- Is x86_64: ").append("x86_64".equals(abi)).append("\n");
        
        if ("x86_64".equals(abi)) {
            diagnostics.append("\n⚠️ KNOWN COMPATIBILITY ISSUES:\n");
            diagnostics.append("- MediaPipe Tasks Vision native libraries may be missing for x86_64\n");
            diagnostics.append("- libmediapipe_tasks_vision_jni.so not available for this architecture\n");
            diagnostics.append("- Google MediaPipe primarily supports ARM architectures\n");
            
            diagnostics.append("\n💡 RECOMMENDED SOLUTIONS:\n");
            diagnostics.append("1. Use ARM64 emulator (Pixel with arm64-v8a)\n");
            diagnostics.append("2. Test on physical Android device\n");
            diagnostics.append("3. Use camera fallback mode (current implementation)\n");
            diagnostics.append("4. Consider alternative gesture detection for x86_64\n");
        }
        
        diagnostics.append("\n- Library Loading Status: ").append(libraryLoaded ? "Success" : "Failed").append("\n");
        diagnostics.append("- Initialization Status: ").append(isInitialized ? "Success" : "Failed").append("\n");
        
        return diagnostics.toString();
    }

    /**
     * Get initialization status as string for debugging
     */
    public static String getStatus() {
        if (hasX86_64Issues()) {
            return "MediaPipe unavailable due to x86_64 architecture limitations";
        } else if (isInitialized && libraryLoaded) {
            return "MediaPipe initialized and libraries loaded";
        } else if (isInitialized && !libraryLoaded) {
            return "MediaPipe initialized but libraries failed to load";
        } else if (!isInitialized && libraryLoaded) {
            return "MediaPipe libraries loaded but not initialized";
        } else {
            return "MediaPipe not initialized and libraries not loaded";
        }
    }

    /**
     * Check if MediaPipe libraries are loaded
     */
    public static boolean isLibraryLoaded() {
        return libraryLoaded;
    }

    /**
     * Initialize MediaPipe with context (for additional setup if needed)
     */
    public static boolean initialize(Context context) {
        if (isInitialized) {
            return libraryLoaded;
        }

        Log.d(TAG, "🔧 Initializing MediaPipe with context...");
        
        // PROACTIVE X86_64 CHECK
        if (hasX86_64Issues()) {
            Log.w(TAG, "⚠️ X86_64 ARCHITECTURE DETECTED - MediaPipe compatibility issues expected");
            Log.w(TAG, getX86_64Diagnostics());
            Log.w(TAG, "💡 Proceeding with fallback initialization, but MediaPipe will likely fail");
        }

        try {
            // Ensure libraries are loaded
            if (!libraryLoaded) {
                initializeLibraries(context);
            }
            
            // If standard loading failed, try manual extraction
            if (!libraryLoaded && context != null) {
                Log.d(TAG, "🚑 Standard loading failed, attempting manual extraction...");
                libraryLoaded = tryManualExtractionWithFallback(context, 
                    android.os.Build.SUPPORTED_ABIS[0], "x86");
            }

            if (libraryLoaded) {
                // Log system information for debugging
                logSystemInfo(context);
                isInitialized = true;
                Log.d(TAG, "✅ MediaPipe initialization completed successfully");
                return true;
            } else {
                // Enhanced messaging for x86_64 issues
                if (hasX86_64Issues()) {
                    Log.w(TAG, "❌ MediaPipe initialization failed - EXPECTED on x86_64 emulator");
                    Log.w(TAG, "💡 This is a known limitation of MediaPipe Tasks Vision on x86_64");
                    Log.w(TAG, "💡 Please use ARM64 emulator or physical device for MediaPipe support");
                } else {
                    Log.w(TAG, "⚠️ MediaPipe initialization failed - libraries not loaded");
                }
                Log.w(TAG, "💡 App will continue in camera fallback mode");
                isInitialized = false;
                return false;
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ MediaPipe initialization failed: " + e.getMessage());
            if (hasX86_64Issues()) {
                Log.e(TAG, "💡 This failure is expected on x86_64 architecture");
            }
            return false;
        }
    }

    /**
     * Log system information for debugging
     */
    private static void logSystemInfo(Context context) {
        try {
            Log.d(TAG, "📱 System Info:");
            Log.d(TAG, "   ABI: " + android.os.Build.SUPPORTED_ABIS[0]);
            Log.d(TAG, "   SDK: " + android.os.Build.VERSION.SDK_INT);
            Log.d(TAG, "   Native lib dir: " + context.getApplicationInfo().nativeLibraryDir);
            
            // Check if native library directory exists
            File nativeLibDir = new File(context.getApplicationInfo().nativeLibraryDir);
            if (nativeLibDir.exists()) {
                Log.d(TAG, "   Native lib dir exists: " + nativeLibDir.getAbsolutePath());
                File[] libFiles = nativeLibDir.listFiles();
                if (libFiles != null) {
                    for (File libFile : libFiles) {
                        if (libFile.getName().contains("mediapipe")) {
                            Log.d(TAG, "   Found MediaPipe lib: " + libFile.getName());
                        }
                    }
                }
            } else {
                Log.w(TAG, "   Native lib dir does not exist!");
            }
        } catch (Exception e) {
            Log.w(TAG, "   Failed to log system info: " + e.getMessage());
        }
    }

    /**
     * Force re-initialization (for testing purposes)
     */
    public static void forceReinitialize(Context context) {
        isInitialized = false;
        libraryLoaded = false;
        initializeLibraries(context);
    }
}