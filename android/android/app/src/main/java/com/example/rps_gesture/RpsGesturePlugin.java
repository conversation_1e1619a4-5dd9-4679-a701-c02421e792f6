package com.eddars.rps_gesture;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView; // Added import
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.EventChannel;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.platform.PlatformView; // Added import
import io.flutter.plugin.platform.PlatformViewFactory; // Added import
import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import java.util.Map;
import java.lang.ref.WeakReference; // Added import

/**
 * RpsGesturePlugin
 */
public class RpsGesturePlugin implements FlutterPlugin, ActivityAware {
    private static final String TAG = "RpsGesturePlugin";
    private static final String CHANNEL_NAME = "com.eddars.rps_gesture/gesture";
    private static final String EVENT_CHANNEL_NAME = "com.eddars.rps_gesture/gesture_stream";
    private static final String VIEW_TYPE = "com.eddars.rps_gesture/preview";
    
    private MethodChannel methodChannel;
    private EventChannel eventChannel;
    private RpsMethodHandler methodHandler;
    private RpsStreamHandler streamHandler;
    private Activity activity;
    private Context context;
    private PreviewView previewView; // Added PreviewView reference
    private PlatformViewFactory platformViewFactory; // Added PlatformViewFactory reference

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        this.context = flutterPluginBinding.getApplicationContext();
        
        // Create handlers
        methodHandler = new RpsMethodHandler();
        streamHandler = new RpsStreamHandler();
        
        // Set up method channel
        methodChannel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL_NAME);
        methodChannel.setMethodCallHandler(methodHandler);
        
        // Set up event channel
        eventChannel = new EventChannel(flutterPluginBinding.getBinaryMessenger(), EVENT_CHANNEL_NAME);
        eventChannel.setStreamHandler(streamHandler);
        
        // Initialize handlers with context
        methodHandler.initialize(context, streamHandler);
        
        // Register platform view factory
        platformViewFactory = new PreviewViewFactory(new WeakReference<>(this));
        flutterPluginBinding.getPlatformViewRegistry().registerViewFactory(VIEW_TYPE, platformViewFactory);
        Log.d(TAG, "Platform view factory registered for type: " + VIEW_TYPE);
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        if (methodChannel != null) {
            methodChannel.setMethodCallHandler(null);
            methodChannel = null;
        }
        
        if (eventChannel != null) {
            eventChannel.setStreamHandler(null);
            eventChannel = null;
        }
        
        if (methodHandler != null) {
            methodHandler.dispose();
            methodHandler = null;
        }
        
        if (streamHandler != null) {
            streamHandler.dispose();
            streamHandler = null;
        }
        
        platformViewFactory = null;
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        this.activity = binding.getActivity();
        Log.d(TAG, "Activity attached: " + (activity != null ? activity.getClass().getSimpleName() : "null"));
        if (methodHandler != null) {
            methodHandler.setActivity(activity);
        }
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        onDetachedFromActivity();
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        onAttachedToActivity(binding);
    }

    @Override
    public void onDetachedFromActivity() {
        Log.d(TAG, "Activity detached");
        if (methodHandler != null) {
            methodHandler.setActivity(null);
        }
        this.activity = null;
    }
    
    // Method to get activity
    public Activity getActivity() {
        return activity;
    }
    
    // Method to set PreviewView in method handler
    public void setPreviewView(PreviewView previewView) {
        this.previewView = previewView;
        Log.d(TAG, "PreviewView set in plugin. PreviewView is " + (previewView != null ? "NOT NULL" : "NULL"));
        if (previewView != null) {
            Log.d(TAG, "PreviewView dimensions: " + previewView.getWidth() + "x" + previewView.getHeight());
        }
        if (methodHandler != null) {
            methodHandler.setPreviewView(previewView);
        }
    }
    
    // PreviewViewFactory class
    private static class PreviewViewFactory extends PlatformViewFactory {
        private final WeakReference<RpsGesturePlugin> pluginRef;

        PreviewViewFactory(WeakReference<RpsGesturePlugin> pluginRef) {
            super(null);
            this.pluginRef = pluginRef;
        }

        @NonNull
        @Override
        public PlatformView create(@NonNull Context context, int viewId, @Nullable Object args) {
            RpsGesturePlugin plugin = pluginRef.get();
            if (plugin == null) {
                Log.e(TAG, "Plugin is null when creating PreviewView");
                throw new IllegalStateException("Plugin is null");
            }
            
            Log.d(TAG, "Creating PreviewViewPlatformView for viewId: " + viewId);
            return new PreviewViewPlatformView(context, plugin);
        }
    }
    
    // PreviewViewPlatformView class
    private static class PreviewViewPlatformView implements PlatformView {
        private final PreviewView previewView;
        private final RpsGesturePlugin plugin;

        PreviewViewPlatformView(Context context, RpsGesturePlugin plugin) {
            Log.d(TAG, "Creating new PreviewView");
            this.previewView = new PreviewView(context);
            this.plugin = plugin;
            plugin.setPreviewView(previewView);
            Log.d(TAG, "PreviewView created and set in plugin");
        }

        @NonNull
        @Override
        public View getView() {
            Log.d(TAG, "Returning PreviewView from getView()");
            return previewView;
        }

        @Override
        public void dispose() {
            Log.d(TAG, "PreviewViewPlatformView disposed");
            // Clean up if needed
        }
    }
}