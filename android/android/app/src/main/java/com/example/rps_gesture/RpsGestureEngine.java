package com.eddars.rps_gesture;

import android.app.Activity;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageProxy;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView; // Added import
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.mediapipe.tasks.components.containers.Category;
import com.google.mediapipe.tasks.core.BaseOptions;
import com.google.mediapipe.tasks.core.Delegate;
import com.google.mediapipe.tasks.vision.core.ImageProcessingOptions;
import com.google.mediapipe.tasks.vision.core.RunningMode;
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizer;
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizerResult;
import com.google.mediapipe.framework.image.BitmapImageBuilder;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;

/**
 * RpsGestureEngine handles MediaPipe gesture recognition
 */
public class RpsGestureEngine {
    private static final String TAG = "RpsGestureEngine";
    private static final String MODEL_FILE = "gesture_recognizer.task";
    
    // Note: Native libraries are now loaded via MediaPipeInitializer for better error handling
    // This avoids static loading that could crash the app if libraries are missing
    
    private Context context;
    private RpsStreamHandler streamHandler;
    private GestureRecognizer gestureRecognizer;
    private ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
    private ExecutorService backgroundExecutor;
    private boolean isDetecting = false;
    private float confidenceThreshold = 0.6f;
    
    /**
     * Constructor for RpsGestureEngine
     */
    public RpsGestureEngine(Context context, RpsStreamHandler streamHandler) {
        this.context = context;
        this.streamHandler = streamHandler;
        Log.d(TAG, "RpsGestureEngine created with context and stream handler");
    }
    
    /**
     * Check if the gesture recognition service is available
     */
    public boolean isAvailable() {
        try {
            return MediaPipeInitializer.isAvailable();
        } catch (Exception e) {
            Log.e(TAG, "Error checking availability: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check MediaPipe availability with detailed diagnosis
     */
    public boolean checkMediaPipeAvailability() {
        try {
            Log.d(TAG, "🔍 DIAGNOSTICS: Checking MediaPipe availability");
            
            // Check MediaPipe initialization status
            boolean available = MediaPipeInitializer.isAvailable();
            Log.d(TAG, "🔍 DIAGNOSTICS: MediaPipe available: " + available);
            
            if (!available) {
                Log.d(TAG, "🔍 DIAGNOSTICS: MediaPipe status: " + MediaPipeInitializer.getStatus());
                
                // Architecture-specific diagnostics
                String abi = android.os.Build.SUPPORTED_ABIS[0];
                Log.d(TAG, "🔍 DIAGNOSTICS: Device ABI: " + abi);
                
                if ("x86_64".equals(abi)) {
                    Log.d(TAG, "🔍 DIAGNOSTICS: X86_64 architecture detected - MediaPipe not supported");
                    Log.d(TAG, MediaPipeInitializer.getX86_64Diagnostics());
                }
            }
            
            return available;
        } catch (Exception e) {
            Log.e(TAG, "🔍 DIAGNOSTICS: Error during availability check: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get system diagnostics information
     */
    public String getSystemDiagnostics() {
        StringBuilder diagnostics = new StringBuilder();
        diagnostics.append("=== MediaPipe System Diagnostics ===\n");
        diagnostics.append("Device ABI: ").append(android.os.Build.SUPPORTED_ABIS[0]).append("\n");
        diagnostics.append("Android Version: ").append(android.os.Build.VERSION.RELEASE).append("\n");
        diagnostics.append("SDK Version: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
        diagnostics.append("Device Model: ").append(android.os.Build.MODEL).append("\n");
        diagnostics.append("Device Manufacturer: ").append(android.os.Build.MANUFACTURER).append("\n");
        diagnostics.append("MediaPipe Status: ").append(MediaPipeInitializer.getStatus()).append("\n");
        
        if (MediaPipeInitializer.hasX86_64Issues()) {
            diagnostics.append("X86_64 Issues: ").append(MediaPipeInitializer.getX86_64Diagnostics()).append("\n");
        }
        
        return diagnostics.toString();
    }

    /**
     * Initialize the gesture recognizer
     */
    public boolean initialize() {
        try {
            Log.d(TAG, "🔧 Initializing MediaPipe Tasks Gesture Recognizer");
            
            // PROACTIVE X86_64 COMPATIBILITY CHECK
            if (MediaPipeInitializer.hasX86_64Issues()) {
                Log.w(TAG, "❌ MediaPipe initialization skipped - x86_64 architecture not supported");
                Log.w(TAG, "💡 SOLUTION: Use ARM64 emulator (Pixel with arm64-v8a) or physical device");
                Log.w(TAG, "💡 CURRENT: App will use camera fallback mode");
                return false;
            }
            
            // Check if MediaPipe is available via initializer
            if (!MediaPipeInitializer.isAvailable()) {
                Log.w(TAG, "⚠️ MediaPipe not available - status: " + MediaPipeInitializer.getStatus());
                
                // Provide specific guidance based on architecture
                String abi = android.os.Build.SUPPORTED_ABIS[0];
                if ("x86_64".equals(abi)) {
                    Log.w(TAG, "💡 X86_64 DETECTED: This is the likely cause of MediaPipe failure");
                    Log.w(TAG, MediaPipeInitializer.getX86_64Diagnostics());
                }
                return false;
            }
            
            Log.d(TAG, "✅ MediaPipe libraries confirmed available, creating recognizer...");
            
            // Create base options with model asset path
            BaseOptions baseOptions = BaseOptions.builder()
                    .setModelAssetPath(MODEL_FILE)  // Use asset path as recommended
                    .build();
            
            // Create gesture recognizer options
            GestureRecognizer.GestureRecognizerOptions options = 
                    GestureRecognizer.GestureRecognizerOptions.builder()
                            .setBaseOptions(baseOptions)
                            .setMinHandDetectionConfidence(0.5f)
                            .setMinHandPresenceConfidence(0.5f)
                            .setMinTrackingConfidence(0.3f)
                            .setNumHands(1)
                            .setRunningMode(RunningMode.LIVE_STREAM)
                            .setResultListener(this::onGestureResult)
                            .build();
            
            // Create gesture recognizer
            gestureRecognizer = GestureRecognizer.createFromOptions(context, options);
            
            Log.d(TAG, "✅ MediaPipe Tasks Gesture Recognizer initialized successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error initializing MediaPipe Tasks Gesture Recognizer: " + e.getMessage());
            
            // Provide architecture-specific error guidance
            if (MediaPipeInitializer.hasX86_64Issues()) {
                Log.e(TAG, "💡 This error is EXPECTED on x86_64 emulator - MediaPipe not supported");
                Log.e(TAG, "💡 SOLUTION: Switch to ARM64 emulator or use physical Android device");
            } else {
                Log.e(TAG, "💡 This usually means MediaPipe native libraries are not properly loaded");
            }
            Log.e(TAG, "💡 App will continue without MediaPipe gesture detection");
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Start gesture detection
     */
    public boolean startDetection(Activity activity, PreviewView previewView, String mode, float confidenceThreshold) {
        try {
            Log.d(TAG, "*** STARTING DETECTION IN MODE: " + mode + " ***");
            Log.d(TAG, "startDetection called with PreviewView: " + (previewView != null ? "NOT NULL" : "NULL"));
            if (previewView != null) {
                Log.d(TAG, "PreviewView dimensions: " + previewView.getWidth() + "x" + previewView.getHeight());
            }
            
            if (gestureRecognizer == null) {
                Log.e(TAG, "Gesture recognizer not initialized");
                return false;
            }
            
            if (activity == null) {
                Log.e(TAG, "Activity is null");
                return false;
            }
            
            if (previewView == null) {
                Log.e(TAG, "PreviewView is null - cannot set up camera");
                return false;
            }
            
            this.confidenceThreshold = confidenceThreshold;
            this.isDetecting = true;
            
            // Set up camera
            setupCamera(activity, previewView);
            
            Log.d(TAG, "Gesture detection started in " + mode + " mode with confidence " + confidenceThreshold);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error starting gesture detection", e);
            return false;
        }
    }

    /**
     * Stop gesture detection
     */
    public void stopDetection() {
        try {
            isDetecting = false;
            
            if (gestureRecognizer != null) {
                gestureRecognizer.close();
                gestureRecognizer = null;
            }
            
            if (backgroundExecutor != null) {
                backgroundExecutor.shutdown();
                backgroundExecutor = null;
            }
            
            Log.d(TAG, "Gesture detection stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping gesture detection", e);
        }
    }

    /**
     * Set up camera for gesture detection with fallback
     */
    private void setupCamera(Activity activity, PreviewView previewView) {
        Log.d(TAG, "Setting up camera with PreviewView: " + (previewView != null ? "NOT NULL" : "NULL"));
        if (previewView != null) {
            Log.d(TAG, "PreviewView dimensions: " + previewView.getWidth() + "x" + previewView.getHeight());
        }
        
        if (previewView == null) {
            Log.e(TAG, "PreviewView is null - cannot set up camera");
            return;
        }
        
        cameraProviderFuture = ProcessCameraProvider.getInstance(activity);
        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                cameraProvider.unbindAll(); // Unbind all use cases before rebinding

                // Preview use case
                Preview preview = new Preview.Builder().build();
                Log.d(TAG, "Preview use case created");
                
                // IMPORTANT: Set SurfaceProvider, otherwise no stream
                preview.setSurfaceProvider(previewView.getSurfaceProvider());
                Log.d(TAG, "SurfaceProvider set on Preview");

                // Image analysis use case
                ImageAnalysis imageAnalysis = new ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                        .build();
                Log.d(TAG, "ImageAnalysis use case created");
                
                imageAnalysis.setAnalyzer(backgroundExecutor, this::analyzeImage);

                // Select front camera with fallback to back camera
                CameraSelector selector = CameraSelector.DEFAULT_FRONT_CAMERA;
                try {
                    if (!cameraProvider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
                        Log.w(TAG, "Front camera not available, falling back to back camera");
                        selector = CameraSelector.DEFAULT_BACK_CAMERA;
                    }
                } catch (Exception ignore) {
                    Log.w(TAG, "Error checking front camera availability, using back camera as fallback");
                    selector = CameraSelector.DEFAULT_BACK_CAMERA;
                }
                
                // Optional, so rotation matches:
                imageAnalysis.setTargetRotation(previewView.getDisplay().getRotation());

                // Bind use cases to camera
                cameraProvider.bindToLifecycle((LifecycleOwner) activity, 
                        selector, preview, imageAnalysis);
                        
                Log.d(TAG, "Camera set up successfully with " + 
                    (selector == CameraSelector.DEFAULT_FRONT_CAMERA ? "front" : "back") + " camera");
            } catch (Exception e) {
                Log.e(TAG, "Error setting up camera", e);
            }
        }, ContextCompat.getMainExecutor(activity));
    }

    /**
     * Analyze camera image
     */
    private void analyzeImage(ImageProxy image) {
        // Log to verify stream
        Log.d(TAG, "Frame received: " + image.getImageInfo().getTimestamp());
        
        if (!isDetecting || gestureRecognizer == null) {
            image.close();
            return;
        }
        
        try {
            // Convert ImageProxy to MediaPipe Image
            com.google.mediapipe.framework.image.MPImage mpImage = convertImageProxyToMediaPipeImage(image);
            
            // Process the image with gesture recognizer
            ImageProcessingOptions options = ImageProcessingOptions.builder()
                    .setRotationDegrees(image.getImageInfo().getRotationDegrees())
                    .build();
            
            // For LIVE_STREAM mode, use recognizeAsync with timestamp
            long timestampMs = System.currentTimeMillis();
            gestureRecognizer.recognizeAsync(mpImage, options, timestampMs);
            
            image.close();
        } catch (RuntimeException e) {
            Log.e(TAG, "Runtime error analyzing image", e);
            if (streamHandler != null) {
                streamHandler.sendError("GESTURE_ERROR", e.getMessage(), null);
            }
            image.close();
        } catch (Exception e) {
            Log.e(TAG, "Error analyzing image", e);
            image.close();
        }
    }

    /**
     * Convert ImageProxy to MediaPipe Image
     */
    private com.google.mediapipe.framework.image.MPImage convertImageProxyToMediaPipeImage(ImageProxy imageProxy) {
        // Convert YUV_420_888 to Bitmap
        ByteBuffer yBuffer = imageProxy.getPlanes()[0].getBuffer();
        ByteBuffer uBuffer = imageProxy.getPlanes()[1].getBuffer();
        ByteBuffer vBuffer = imageProxy.getPlanes()[2].getBuffer();

        int ySize = yBuffer.remaining();
        int uSize = uBuffer.remaining();
        int vSize = vBuffer.remaining();

        byte[] nv21 = new byte[ySize + uSize + vSize];

        yBuffer.get(nv21, 0, ySize);
        vBuffer.get(nv21, ySize, vSize);
        uBuffer.get(nv21, ySize + vSize, uSize);

        YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, 
                imageProxy.getWidth(), imageProxy.getHeight(), null);
        
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        yuvImage.compressToJpeg(new Rect(0, 0, yuvImage.getWidth(), yuvImage.getHeight()), 
                100, out);
        
        byte[] imageBytes = out.toByteArray();
        Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
        
        // Convert Bitmap to MediaPipe Image
        return new BitmapImageBuilder(bitmap).build();
    }

    /**
     * Handle gesture recognition results with enhanced custom classification
     */
    private void onGestureResult(GestureRecognizerResult result, com.google.mediapipe.framework.image.MPImage unused) {
        try {
            if (!isDetecting || streamHandler == null) {
                return;
            }
            
            // Enhanced gesture classification with custom logic
            String finalGesture = "None";
            float finalConfidence = 0.0f;
            
            // Get all gesture classifications for analysis
            if (!result.gestures().isEmpty()) {
                List<Category> categories = result.gestures().get(0);
                
                if (!categories.isEmpty()) {
                    // Analyze all detected gestures, not just the top one
                    String topGesture = categories.get(0).categoryName();
                    float topConfidence = categories.get(0).score();
                    
                    Log.d(TAG, "=== GESTURE ANALYSIS ===");
                    Log.d(TAG, "Top gesture: " + topGesture + " (confidence: " + topConfidence + ")");
                    
                    // Log all detected gestures for analysis
                    for (int i = 0; i < Math.min(categories.size(), 3); i++) {
                        Category cat = categories.get(i);
                        Log.d(TAG, "Gesture " + (i+1) + ": " + cat.categoryName() + " (" + cat.score() + ")");
                    }
                    
                    // Apply enhanced classification logic
                    String classifiedGesture = enhancedGestureClassification(categories, topGesture, topConfidence);
                    
                    if (classifiedGesture != null && !"None".equals(classifiedGesture)) {
                        finalGesture = classifiedGesture;
                        finalConfidence = topConfidence;
                        Log.d(TAG, "*** FINAL CLASSIFICATION: " + finalGesture + " ***");
                        streamHandler.sendGestureResult(finalGesture, finalConfidence);
                    } else {
                        Log.d(TAG, "*** REJECTED - sending None ***");
                        streamHandler.sendGestureResult("None", 1.0f);
                    }
                    
                    Log.d(TAG, "========================");
                }
            } else {
                Log.d(TAG, "No gestures detected in frame");
                streamHandler.sendGestureResult("None", 1.0f);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling gesture result", e);
            if (streamHandler != null) {
                streamHandler.sendError("GESTURE_PROCESSING_ERROR", e.getMessage(), null);
            }
        }
    }
    
    /**
     * Enhanced gesture classification with custom logic to improve accuracy
     */
    private String enhancedGestureClassification(List<Category> categories, String topGesture, float topConfidence) {
        // EXTREMELY AGGRESSIVE FILTERING TO STOP RANDOM SWITCHING
        
        // Base threshold - much higher than before
        if (topConfidence < 0.6f) {
            Log.d(TAG, "REJECT: Confidence too low (" + topConfidence + " < 0.6)");
            return null;
        }
        
        // Special handling for Victory (scissors) - FIXED CONFIDENCE ISSUE
        if ("Victory".equals(topGesture)) {
            // For Victory, require VERY high confidence to avoid false positives
            if (topConfidence >= 0.75f) {  // Much higher than before
                // Check margin against other gestures
                float secondHighest = 0.0f;
                String secondGesture = "";
                for (int i = 1; i < categories.size(); i++) {
                    float conf = categories.get(i).score();
                    if (conf > secondHighest) {
                        secondHighest = conf;
                        secondGesture = categories.get(i).categoryName();
                    }
                }
                
                // Victory should be MUCH higher than the second best (0.25 margin)
                if (topConfidence - secondHighest >= 0.25f) {
                    Log.d(TAG, "*** ACCEPT Victory: confidence=" + topConfidence + ", margin=" + (topConfidence - secondHighest) + " vs " + secondGesture + " ***");
                    return "Victory";
                } else {
                    Log.d(TAG, "REJECT Victory: insufficient margin (" + (topConfidence - secondHighest) + " vs " + secondGesture + ")");
                    return null;
                }
            } else {
                Log.d(TAG, "REJECT Victory: confidence too low (" + topConfidence + " < 0.75)");
                return null;
            }
        }
        
        // For other gestures, apply VERY HIGH thresholds to stop random switching
        float requiredConfidence = 0.85f;  // Much higher than before
        if ("Closed_Fist".equals(topGesture)) {
            requiredConfidence = 0.8f;  // Rock is usually more stable
        } else if ("Open_Palm".equals(topGesture)) {
            requiredConfidence = 0.8f;  // Paper is usually more stable
        }
        
        // Also check margin for all gestures
        float secondHighest = 0.0f;
        for (int i = 1; i < categories.size(); i++) {
            float conf = categories.get(i).score();
            if (conf > secondHighest) {
                secondHighest = conf;
            }
        }
        
        // Require significant margin for all gestures
        if (topConfidence >= requiredConfidence && (topConfidence - secondHighest) >= 0.2f) {
            Log.d(TAG, "*** ACCEPT " + topGesture + ": confidence=" + topConfidence + ", margin=" + (topConfidence - secondHighest) + " ***");
            return topGesture;
        } else {
            Log.d(TAG, "REJECT " + topGesture + ": confidence=" + topConfidence + " (req: " + requiredConfidence + "), margin=" + (topConfidence - secondHighest) + " (req: 0.2)");
            return null;
        }
    }



}