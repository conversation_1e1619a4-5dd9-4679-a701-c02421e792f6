package com.eddars.rps_gesture;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.EventChannel;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * RpsStreamHandler handles streaming gesture detection results to Flutter
 */
public class RpsStreamHandler implements EventChannel.StreamHandler {
    private static final String TAG = "RpsStreamHandler";
    private EventChannel.EventSink eventSink;
    private final ConcurrentLinkedQueue<Map<String, Object>> pendingEvents = new ConcurrentLinkedQueue<>();

    @Override
    public void onListen(Object arguments, EventChannel.EventSink events) {
        Log.d(TAG, "Stream listener attached");
        this.eventSink = events;
        
        // Send any pending events
        while (!pendingEvents.isEmpty()) {
            Map<String, Object> event = pendingEvents.poll();
            if (event != null && events != null) {
                events.success(event);
            }
        }
    }

    @Override
    public void onCancel(Object arguments) {
        Log.d(TAG, "Stream listener detached");
        this.eventSink = null;
    }

    /**
     * Send gesture detection result to Flutter
     */
    public void sendGestureResult(String gesture, float confidence) {
        Map<String, Object> result = new HashMap<>();
        result.put("gesture", gesture);
        result.put("confidence", confidence);
        
        if (eventSink != null) {
            eventSink.success(result);
        } else {
            // Queue the event until a listener is attached
            pendingEvents.offer(result);
            Log.d(TAG, "Queued gesture result: " + gesture + " (" + confidence + ")");
        }
    }

    /**
     * Send error to Flutter
     */
    public void sendError(String errorCode, String errorMessage, @Nullable Object errorDetails) {
        if (eventSink != null) {
            eventSink.error(errorCode, errorMessage, errorDetails);
        } else {
            Log.e(TAG, "Gesture detection error: " + errorCode + " - " + errorMessage);
        }
    }

    public void dispose() {
        pendingEvents.clear();
        eventSink = null;
    }
}