package com.eddars.rps_gesture;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView; // Added import
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView; // Added import
import java.util.HashMap;
import java.util.Map;

/**
 * RpsMethodHandler handles method calls from Flutter
 */
public class RpsMethodHandler implements MethodChannel.MethodCallHandler {
    private static final String TAG = "RpsMethodHandler";
    private Context context;
    private Activity activity;
    private RpsStreamHandler streamHandler;
    private RpsGestureEngine gestureEngine;
    private boolean isInitialized = false;
    private PreviewView previewView; // Added PreviewView reference

    public void initialize(Context context, RpsStreamHandler streamHandler) {
        this.context = context;
        this.streamHandler = streamHandler;
        Log.d(TAG, "Method handler initialized");
    }

    public void setActivity(Activity activity) {
        this.activity = activity;
    }
    
    // Method to set PreviewView
    public void setPreviewView(PreviewView previewView) {
        this.previewView = previewView;
        Log.d(TAG, "PreviewView set in method handler. PreviewView is " + (previewView != null ? "NOT NULL" : "NULL"));
        if (previewView != null) {
            Log.d(TAG, "PreviewView dimensions: " + previewView.getWidth() + "x" + previewView.getHeight());
        }
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        try {
            switch (call.method) {
                case "initialize":
                    handleInitialize(result);
                    break;
                case "startDetection":
                    handleStartDetection(call, result);
                    break;
                case "stopDetection":
                    handleStopDetection(result);
                    break;
                case "isAvailable":
                    handleIsAvailable(result);
                    break;
                case "checkMediaPipeAvailability":
                    handleCheckMediaPipeAvailability(result);
                    break;
                case "getSystemDiagnostics":
                    handleGetSystemDiagnostics(result);
                    break;
                default:
                    result.notImplemented();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling method call: " + call.method, e);
            result.error("METHOD_ERROR", e.getMessage(), null);
        }
    }

    private void handleInitialize(MethodChannel.Result result) {
        try {
            if (context == null) {
                result.success(false);
                return;
            }

            // Initialize the gesture engine
            gestureEngine = new RpsGestureEngine(context, streamHandler);
            boolean success = gestureEngine.initialize();
            
            isInitialized = success;
            Log.d(TAG, "Gesture engine initialization: " + success);
            result.success(success);
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library", e);
            result.success(false);
        } catch (Exception e) {
            Log.e(TAG, "Error initializing gesture engine", e);
            result.success(false);
        }
    }

    private void handleStartDetection(MethodCall call, MethodChannel.Result result) {
        // Add activity null check
        if (activity == null) {
            Log.e(TAG, "Activity is null during startDetection");
            result.success(false);
            return;
        }
        
        // Add previewView null check
        if (previewView == null) {
            Log.e(TAG, "PreviewView is null during startDetection - CameraX binding will fail");
            result.success(false);
            return;
        }
        
        Log.d(TAG, "Starting detection with PreviewView: " + (previewView != null ? "AVAILABLE" : "NULL"));
        if (previewView != null) {
            Log.d(TAG, "PreviewView dimensions: " + previewView.getWidth() + "x" + previewView.getHeight());
        }
        
        if (!isInitialized || gestureEngine == null) {
            Log.w(TAG, "Gesture engine not initialized");
            result.success(false);
            return;
        }

        try {
            String mode = call.argument("mode");
            Double confidenceThreshold = call.argument("confidenceThreshold");
            
            if (mode == null) mode = "GAME";
            if (confidenceThreshold == null) confidenceThreshold = 0.6;
            
            // Pass PreviewView to startDetection
            boolean success = gestureEngine.startDetection(activity, previewView, mode, confidenceThreshold.floatValue());
            result.success(success);
        } catch (Exception e) {
            Log.e(TAG, "Error starting detection", e);
            result.success(false);
        }
    }

    private void handleStopDetection(MethodChannel.Result result) {
        if (!isInitialized || gestureEngine == null) {
            result.success(true); // Already stopped
            return;
        }

        try {
            gestureEngine.stopDetection();
            result.success(true);
        } catch (Exception e) {
            Log.e(TAG, "Error stopping detection", e);
            result.success(false);
        }
    }
    
    private void handleIsAvailable(MethodChannel.Result result) {
        try {
            if (gestureEngine != null) {
                result.success(gestureEngine.isAvailable());
            } else {
                // Create temporary engine to check availability
                RpsGestureEngine tempEngine = new RpsGestureEngine(context, streamHandler);
                result.success(tempEngine.isAvailable());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking availability", e);
            result.success(false);
        }
    }
    
    private void handleCheckMediaPipeAvailability(MethodChannel.Result result) {
        try {
            if (gestureEngine != null) {
                result.success(gestureEngine.checkMediaPipeAvailability());
            } else {
                // Create temporary engine to check MediaPipe availability
                RpsGestureEngine tempEngine = new RpsGestureEngine(context, streamHandler);
                result.success(tempEngine.checkMediaPipeAvailability());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking MediaPipe availability", e);
            result.success(false);
        }
    }
    
    private void handleGetSystemDiagnostics(MethodChannel.Result result) {
        try {
            if (gestureEngine != null) {
                result.success(gestureEngine.getSystemDiagnostics());
            } else {
                // Create temporary engine to get diagnostics
                RpsGestureEngine tempEngine = new RpsGestureEngine(context, streamHandler);
                result.success(tempEngine.getSystemDiagnostics());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting system diagnostics", e);
            result.success("Error getting diagnostics: " + e.getMessage());
        }
    }

    public void dispose() {
        try {
            if (gestureEngine != null) {
                gestureEngine.stopDetection();
                gestureEngine = null;
            }
            isInitialized = false;
            context = null;
            activity = null;
            streamHandler = null;
            previewView = null; // Clear PreviewView reference
        } catch (Exception e) {
            Log.e(TAG, "Error disposing method handler", e);
        }
    }
}