package com.eddars.rockpaperscissors

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import com.eddars.rockpaperscissors.gesture.MpGestureManager

class MainActivity : FlutterActivity() {

    private lateinit var gestureManager: MpGestureManager

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        val messenger = flutterEngine.dartExecutor.binaryMessenger
        gestureManager = MpGestureManager(this)

        MethodChannel(messenger, "mp_gesture/methods").setMethodCallHandler { call, result ->
            when (call.method) {
                "start" -> {
                    val minScore = (call.argument<Double>("minScore") ?: 0.65)
                    try {
                        gestureManager.start(minScore)
                        result.success(true)
                    } catch (t: Throwable) {
                        result.error("START_ERR", t.message, null)
                    }
                }
                "stop" -> {
                    try {
                        gestureManager.stop()
                        result.success(true)
                    } catch (t: Throwable) {
                        result.error("STOP_ERR", t.message, null)
                    }
                }
                else -> result.notImplemented()
            }
        }

        EventChannel(messenger, "mp_gesture/events").setStreamHandler(gestureManager)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (this::gestureManager.isInitialized) {
            gestureManager.onRequestPermissionsResult(requestCode, grantResults)
        }
    }
}
