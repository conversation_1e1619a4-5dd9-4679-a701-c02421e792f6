package com.eddars.rockpaperscissors.gesture

import android.Manifest
import android.content.pm.PackageManager
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.EventChannel

import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.framework.image.MediaImageBuilder
import com.google.mediapipe.tasks.vision.core.ImageProcessingOptions
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizer
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizerResult
import io.flutter.FlutterInjector

class MpGestureManager(private val activity: FlutterActivity) : EventChannel.StreamHandler {

    private var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>? = null
    private var analysis: ImageAnalysis? = null
    private var recognizer: GestureRecognizer? = null
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.65

    fun start(minScore: Double) {
        this.minScore = minScore
        ensurePermissionAndStart()
    }

    fun stop() {
        try {
            val provider = cameraProviderFuture?.get()
            provider?.unbindAll()
        } catch (_: Throwable) {}
        analysis = null
        recognizer?.close()
        recognizer = null
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
        sink = events
    }

    override fun onCancel(arguments: Any?) {
        sink = null
    }

    fun onRequestPermissionsResult(requestCode: Int, grantResults: IntArray) {
        if (requestCode == 9001 && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startCameraAndRecognizer()
        }
    }

    private fun ensurePermissionAndStart() {
        val granted = ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        if (!granted) {
            ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.CAMERA), 9001)
            return
        }
        startCameraAndRecognizer()
    }

    private fun startCameraAndRecognizer() {
        if (recognizer == null) {
            val modelPath = ensureModelFile("assets/models/gesture_recognizer.task")
            recognizer = GestureRecognizer.createFromFile(activity, modelPath)
        }

        cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
        cameraProviderFuture?.addListener({
            val provider = cameraProviderFuture?.get() ?: return@addListener
            provider.unbindAll()

            val analyzer = ImageAnalysis.Builder()
                .setTargetResolution(Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build()

            analyzer.setAnalyzer(activity.mainExecutor) { imageProxy ->
                analyzeFrame(imageProxy)
            }

            val cameraSelector = CameraSelector.Builder()
                .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                .build()

            analysis = analyzer
            provider.bindToLifecycle(activity, cameraSelector, analyzer)
        }, activity.mainExecutor)
    }

    private fun analyzeFrame(image: ImageProxy) {
        try {
            val mediaImage = image.image ?: run { image.close(); return }
            val mpImage: MPImage = MediaImageBuilder(mediaImage).build()
            val options = ImageProcessingOptions.builder()
                .setRotationDegrees(image.imageInfo.rotationDegrees)
                .build()
            val result: GestureRecognizerResult? = recognizer?.recognize(mpImage, options)
            handleResult(result)
        } catch (_: Throwable) {
            // ignore errors per-frame
        } finally {
            image.close()
        }
    }

    private fun handleResult(result: GestureRecognizerResult?) {
        if (result == null) return
        val gestures = result.gestures() ?: return
        if (gestures.isEmpty()) {
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }
        val firstList = gestures[0] ?: return
        if (firstList.isEmpty()) {
            sink?.success(mapOf("label" to "none", "score" to 0.0, "ts" to System.currentTimeMillis()))
            return
        }
        val top = firstList[0]
        val label = if (top.score() >= minScore) top.categoryName() else "none"
        sink?.success(mapOf(
            "label" to label,
            "score" to top.score().toDouble(),
            "ts" to System.currentTimeMillis()
        ))
    }

    // Copy Flutter asset to a real file path, so createFromFile can read it.
    private fun ensureModelFile(assetPath: String): String {
        val loader = FlutterInjector.instance().flutterLoader()
        val key = loader.getLookupKeyForAsset(assetPath)
        val outFile = java.io.File(activity.filesDir, "gesture_recognizer.task")
        if (outFile.exists() && outFile.length() > 0) return outFile.absolutePath

        activity.assets.open(key).use { input ->
            outFile.outputStream().use { output ->
                input.copyTo(output)
            }
        }
        return outFile.absolutePath
    }
}
