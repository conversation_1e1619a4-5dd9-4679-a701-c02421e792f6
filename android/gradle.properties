# Optimisation mémoire Gradle pour éviter le thrashing du GC
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:MaxGCPauseMillis=200
android.useAndroidX=true
android.enableJetifier=true

# Performance et optimisations de timeout
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.workers.max=2
android.enableD8.desugaring=true
android.enableR8.fullMode=false

# Augmenter les timeouts pour l'installation
android.injected.testOnly=false

# Optimisations spécifiques pour MediaPipe
android.enableBuildCache=false
org.gradle.caching=false
