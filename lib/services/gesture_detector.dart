// lib/services/gesture_detector.dart
//
// Compatibility adapter so existing code keeps working.
//
// Exposes:
//   - gesture_service.HandGestureDetector
//   - gesture_service.GestureType (typedef)
// and bridges to ProHandGestureDetector (native MediaPipe).

import 'dart:async';
import 'package:camera/camera.dart' show CameraController;
import 'package:rock_paper_scissors_flutter/services/pro_hand_gesture_detector.dart' as core;

// Re-export enum name
typedef GestureType = core.GestureType;

class HandGestureDetector {
  final core.ProHandGestureDetector _detector = core.ProHandGestureDetector();

  // ---- State / getters ----
  Stream<GestureType>? get gestureStream => _detector.gestureStream;
  GestureType get currentGesture => _detector.currentGesture;
  String get currentMode => _detector.currentMode;
  double get currentConfidence => _detector.currentConfidence;
  String get detectedHand => _detector.detectedHand;  // Main détectée automatiquement
  bool get isInitialized => _detector.isInitialized;

  // For legacy references (not used by native path)
  CameraController? get cameraController => _detector.cameraController;

  // ---- Lifecycle ----
  Future<bool> initialize() async {
    return await _detector.initialize();
  }

  Future<bool> startDetection({String mode = core.ProHandGestureDetector.MODE_GAME}) async {
    await _detector.startDetection(mode: mode);
    return true;
  }

  Future<bool> stopDetection() async {
    await _detector.stopDetection();
    return true;
  }

  // ---- Helpers kept for legacy call sites ----
  String getGestureString() => _detector.getGestureString();

  // Some places expect a String from getGesture()
  String getGesture() => _detector.getGestureString();

  Future<void> dispose() async {
    _detector.dispose();
  }
}
