import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class SettingsProvider extends ChangeNotifier {
  // Settings variables
  String? _selectedImage;
  String? _selectedMusic;
  String _selectedSineshifterSound = "watt.mp3";
  int _selectedLives = 3;
  String _selectedDifficulty = "medium";
  int _volumeLevel = 50;
  String _preferredHand = 'right';

  // Available options (would be populated from assets)
  List<String> _bgImages = [];
  List<String> _bgMusic = [];
  List<String> _sineshifterSounds = [];

  // Getters
  String? get selectedImage => _selectedImage;
  String? get selectedMusic => _selectedMusic;
  String get selectedSineshifterSound => _selectedSineshifterSound;
  int get selectedLives => _selectedLives;
  String get selectedDifficulty => _selectedDifficulty;
  int get volumeLevel => _volumeLevel;
  String get preferredHand => _preferredHand;
  List<String> get bgImages => _bgImages;
  List<String> get bgMusic => _bgMusic;
  List<String> get sineshifterSounds => _sineshifterSounds;

  SettingsProvider() {
    _initializeAssetLists();
  }

  void _initializeAssetLists() {
    // Background images - actual files that exist
    _bgImages = [
      'bg_img_1.webp',
      'bg_img_2.webp',
      'bg_img_3.webp',
    ];
    
    // Background music - actual files that exist
    _bgMusic = [
      'sunrise1.mp3',
      'sunrise2.mp3',
      'sunset1.mp3',
      'sunset2.mp3',
      'midnight1.mp3',
      'midnight2.mp3',
      'desertWind.mp3',
      'hopeOfLife.mp3',
      'laVieEstBelle.mp3',
    ];
    
    // 3_2_go sounds - actual files that exist (14 files)
    _sineshifterSounds = [
      'watt.mp3',
      'alien.mp3',
      'archetype.mp3',
      'chipsound.mp3',
      'deeply.mp3',
      'kamoni.mp3',
      'maddog.mp3',
      'pimp.mp3',
      'sineshifter.mp3',
      'smooth.mp3',
      'square.mp3',
      'uplifter.mp3',
      'visible.mp3',
      'wetwood.mp3',
    ];
  }

  // Setters
  void setSelectedImage(String? image) {
    _selectedImage = image;
    notifyListeners();
  }

  void setSelectedMusic(String? music) {
    _selectedMusic = music;
    notifyListeners();
  }

  void setSelectedSineshifterSound(String sound) {
    _selectedSineshifterSound = sound;
    notifyListeners();
  }

  void setSelectedLives(int lives) {
    _selectedLives = lives;
    notifyListeners();
  }

  void setSelectedDifficulty(String difficulty) {
    _selectedDifficulty = difficulty;
    notifyListeners();
  }

  void setVolumeLevel(int volume) {
    _volumeLevel = volume;
    notifyListeners();
  }

  
void setPreferredHand(String hand) {
  _preferredHand = (hand == 'left') ? 'left' : 'right';
  notifyListeners();
}

// Save/Load settings

  Future<void> saveSettings() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/settings.json');
      
      Map<String, dynamic> settings = {
        "volume": _volumeLevel,
        "bg_image": _selectedImage,
        "bg_music": _selectedMusic,
        "sineshifter_sound": _selectedSineshifterSound,
        "lives": _selectedLives,
        "difficulty": _selectedDifficulty,
        "preferred_hand": _preferredHand,
      };
      
      await file.writeAsString(json.encode(settings));
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  Future<void> loadSettings() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/settings.json');
      
      if (await file.exists()) {
        String contents = await file.readAsString();
        Map<String, dynamic> settings = json.decode(contents);
        
        _volumeLevel = settings["volume"] ?? 50;
        _selectedImage = settings["bg_image"];
        _selectedMusic = settings["bg_music"];
        _selectedSineshifterSound = settings["sineshifter_sound"] ?? "watt.mp3";
        _selectedLives = settings["lives"] ?? 3;
        _selectedDifficulty = settings["difficulty"] ?? "medium";
        
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  // Apply all current settings
  void applyAllSettings() {
    // This method can be called to ensure all settings are applied
    // The actual application logic is handled by GameStateProvider
    notifyListeners();
  }
}