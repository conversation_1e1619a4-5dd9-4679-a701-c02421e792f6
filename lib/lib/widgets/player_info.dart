import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_state_provider.dart';

class PlayerInfo extends StatelessWidget {
  const PlayerInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameStateProvider>(
      builder: (context, gameState, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(15),
          ),
          child: Text(
            gameState.currentPlayer.isNotEmpty 
                ? 'Player: ${gameState.currentPlayer}' 
                : 'Player',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontFamily: 'Genos',
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      },
    );
  }
}