import 'package:flutter/material.dart';

class CountdownDisplay extends StatelessWidget {
  final String countdownImage;

  const CountdownDisplay({
    super.key,
    required this.countdownImage,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Image.asset(
        'assets/images/$countdownImage',
        width: MediaQuery.of(context).size.width * 0.6,
        height: MediaQuery.of(context).size.width * 0.6,
      ),
    );
  }
}