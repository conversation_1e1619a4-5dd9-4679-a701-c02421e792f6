import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_state_provider.dart';

class ScoreDisplay extends StatelessWidget {
  const ScoreDisplay({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameStateProvider>(
      builder: (context, gameState, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(15),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Player icon and score
              Image.asset(
                'assets/icons/personne-icon.png',
                width: 20,
                height: 20,
              ),
              const SizedBox(width: 4),
              Text(
                '${gameState.scorePlayer}',
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Genos',
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Separator
              const Text(
                '|',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Program score and icon
              Text(
                '${gameState.scoreProgram}',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Genos',
                ),
              ),
              const SizedBox(width: 4),
              Image.asset(
                'assets/icons/robot-icon.png',
                width: 20,
                height: 20,
              ),
            ],
          ),
        );
      },
    );
  }
}