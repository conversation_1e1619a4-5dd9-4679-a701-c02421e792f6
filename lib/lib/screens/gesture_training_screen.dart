import 'dart:async';
import 'package:flutter/material.dart';
import 'package:rock_paper_scissors_flutter/services/pro_hand_gesture_detector.dart';


class GestureTrainingScreen extends StatefulWidget {
 const GestureTrainingScreen({super.key});


 @override
 State<GestureTrainingScreen> createState() => _GestureTrainingScreenState();
}


class _GestureTrainingScreenState extends State<GestureTrainingScreen> {
 late final ProHandGestureDetector _gestureDetector;
 bool _isTraining = false;
 GestureType _currentGesture = GestureType.none;
 StreamSubscription<GestureType>? _gestureSubscription;
 bool _isInitialized = false;
 String _errorMessage = '';


 double _detectionAccuracy = 0.0;
 int _totalDetections = 0;
 int _goodDetections = 0;


 double _currentConfidence = 0.0;
 static const double CONFIDENCE_THRESHOLD = 0.60;


 Timer? _pollTimer;


 @override
 void initState() {
   super.initState();
   _gestureDetector = ProHandGestureDetector();
   _initializeGestureDetector();
 }


 Future<void> _initializeGestureDetector() async {
   try {
     final result = await _gestureDetector.initialize();
     if (result && mounted) {
       setState(() {
         _isInitialized = true;
         _errorMessage = '';
       });
     } else if (mounted) {
       setState(() {
         _errorMessage =
             'Failed to initialize gesture detection. Please check camera permissions.';
         _isInitialized = false;
       });
     }
   } catch (e) {
     if (mounted) {
       setState(() {
         _errorMessage = 'Initialization error: ${e.toString()}';
         _isInitialized = false;
       });
     }
   }
 }


 Future<void> _startTraining() async {
   if (_isTraining || !_isInitialized) return;


   setState(() {
     _isTraining = true;
     _currentGesture = GestureType.none;
     _errorMessage = '';
     _totalDetections = 0;
     _goodDetections = 0;
     _detectionAccuracy = 0.0;
   });


   try {
     await _gestureDetector.startDetection(
         mode: ProHandGestureDetector.MODE_TRAINING);


     _gestureSubscription?.cancel();
     _pollTimer?.cancel();


     if (_gestureDetector.gestureStream != null) {
       _gestureSubscription =
           _gestureDetector.gestureStream!.listen((gesture) {
         _updateGestureDetection(gesture);
       }, onError: (error) {
         if (mounted) {
           setState(() {
             _errorMessage = 'Detection error: $error';
           });
         }
       });
     } else {
       _startPollingFallback();
     }
   } catch (e) {
     if (mounted) {
       setState(() {
         _errorMessage = 'Failed to start training: $e';
         _isTraining = false;
       });
     }
   }
 }


 void _startPollingFallback() {
   _pollTimer?.cancel();
   _pollTimer = Timer.periodic(const Duration(milliseconds: 400), (timer) {
     if (!_isTraining || !mounted) {
       timer.cancel();
       return;
     }
     try {
       final g = _gestureDetector.currentGesture;
       if (g != GestureType.none && g != GestureType.unknown) {
         _updateGestureDetection(g);
       }
     } catch (_) {}
   });
 }


 void _updateGestureDetection(GestureType gesture) {
   _currentConfidence = _gestureDetector.currentConfidence;
   _totalDetections++;


   if (_currentConfidence >= CONFIDENCE_THRESHOLD &&
       gesture != GestureType.none &&
       gesture != GestureType.unknown) {
     _goodDetections++;
   }


   if (mounted) {
     setState(() {
       _currentGesture = gesture;
       _detectionAccuracy =
           _totalDetections > 0 ? (_goodDetections / _totalDetections) : 0.0;
     });
   }
 }


 Future<void> _stopTraining() async {
   if (!_isTraining) return;
   try {
     _pollTimer?.cancel();
     await _gestureDetector.stopDetection();
     await _gestureSubscription?.cancel();
     _gestureSubscription = null;
   } catch (_) {}
   if (mounted) {
     setState(() {
       _isTraining = false;
       _currentGesture = GestureType.none;
     });
   }
 }


 @override
 void dispose() {
   _pollTimer?.cancel();
   _gestureSubscription?.cancel();
   _gestureDetector.dispose();
   super.dispose();
 }


 String _getGestureImage(GestureType gesture) {
   switch (gesture) {
     case GestureType.rock:
       return 'assets/images/pierre.png';
     case GestureType.paper:
       return 'assets/images/papier.png';
     case GestureType.scissors:
       return 'assets/images/ciseaux.png';
     default:
       return 'assets/images/box_question.png';
   }
 }


 String _getGestureName(GestureType gesture) {
   switch (gesture) {
     case GestureType.rock:
       return 'Rock';
     case GestureType.paper:
       return 'Paper';
     case GestureType.scissors:
       return 'Scissors';
     case GestureType.unknown:
       return 'Unknown';
     default:
       return 'No Gesture';
   }
 }


 Color _getProgressColor() {
   return _detectionAccuracy >= 0.79 ? Colors.green : Colors.red;
 }


 @override
 Widget build(BuildContext context) {
   final screenWidth = MediaQuery.of(context).size.width;
   final displayWidth = screenWidth * 0.9;


   return Scaffold(
     backgroundColor: const Color(0xFF1A1A2E),
     appBar: AppBar(
       title: const Text('Gesture Training',
           style: TextStyle(
             fontSize: 24,
             fontWeight: FontWeight.bold,
             color: Colors.white,
             fontFamily: 'Genos',
           )),
       backgroundColor: Colors.transparent,
       elevation: 0,
       leading: IconButton(
         icon: const Icon(Icons.arrow_back, color: Colors.white),
         onPressed: () {
           _stopTraining();
           Navigator.pop(context);
         },
       ),
     ),
     body: SingleChildScrollView(
       padding: const EdgeInsets.all(20.0),
       child: Column(
         children: [
           Container(
             width: displayWidth,
             height: 420,
             decoration: BoxDecoration(
               color: const Color(0xFF16213E),
               borderRadius: BorderRadius.circular(20),
               border: Border.all(
                 color: Colors.white.withOpacity(0.2),
                 width: 2,
               ),
             ),
             child: Column(
               mainAxisAlignment: MainAxisAlignment.center,
               children: [
                 // PNG display area
                 Container(
                   width: 220,
                   height: 220,
                   decoration: BoxDecoration(
                     color: Colors.black.withOpacity(0.25),
                     borderRadius: BorderRadius.circular(15),
                   ),
                   child: _currentGesture != GestureType.none &&
                           _currentGesture != GestureType.unknown &&
                           _currentConfidence >= CONFIDENCE_THRESHOLD
                       ? ClipRRect(
                           borderRadius: BorderRadius.circular(15),
                           child: Image.asset(
                             _getGestureImage(_currentGesture),
                             fit: BoxFit.contain,
                           ),
                         )
                       : Center(
                           child: Icon(Icons.camera_alt_outlined,
                               size: 64, color: Colors.white.withOpacity(0.35)),
                         ),
                 ),
                 const SizedBox(height: 12),
                 // DEBUG LINE avec information sur la main
                 Text(
                   'Detected: ${_getGestureName(_currentGesture)}  (${(_currentConfidence*100).toStringAsFixed(1)}%)',
                   style: const TextStyle(color: Colors.white70, fontFamily: 'Genos'),
                 ),
                 Text(
                   'Hand: ${_gestureDetector.detectedHand}',
                   style: const TextStyle(color: Colors.white70, fontFamily: 'Genos', fontSize: 12),
                 ),
               ],
             ),
           ),
           const SizedBox(height: 24),
           Row(
             children: [
               Expanded(
                 child: ElevatedButton(
                   onPressed: _isInitialized && _errorMessage.isEmpty
                       ? (_isTraining ? null : _startTraining)
                       : null,
                   child: const Text('Start Training'),
                 ),
               ),
               const SizedBox(width: 12),
               Expanded(
                 child: ElevatedButton(
                   onPressed: _isTraining ? _stopTraining : null,
                   child: const Text('Stop Training'),
                 ),
               ),
             ],
           ),
           if (_errorMessage.isNotEmpty) ...[
             const SizedBox(height: 20),
             Text(_errorMessage, style: const TextStyle(color: Colors.redAccent)),
           ],
         ],
       ),
     ),
   );
 }
}





