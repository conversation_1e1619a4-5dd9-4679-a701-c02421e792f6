import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/game_state_provider.dart';
import '../screens/gesture_training_screen.dart';

class SettingsMenu extends StatefulWidget {
  final VoidCallback onReturnToGame;

  const SettingsMenu({
    super.key,
    required this.onReturnToGame,
  });

  @override
  State<SettingsMenu> createState() => _SettingsMenuState();
}

class _SettingsMenuState extends State<SettingsMenu> {
  @override
  void initState() {
    super.initState();
    // Load settings when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SettingsProvider>(context, listen: false).loadSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Background Image Selection
                _buildSectionHeader('Background Image'),
                _buildImageSelector(settings),
                
                const SizedBox(height: 20),
                
                // Background Music Selection
                _buildSectionHeader('Background Music'),
                _buildMusicSelector(settings),
                
                const SizedBox(height: 20),
                
                // Volume Control
                _buildSectionHeader('Background Music Volume'),
                _buildVolumeSlider(settings),
                
                const SizedBox(height: 20),
                
                // Countdown Sound Selection
                _buildSectionHeader('Countdown Sound'),
                _buildCountdownSoundSelector(settings),
                
                const SizedBox(height: 20),
                
                // Lives Selection
                _buildSectionHeader('Number of Lives'),
                _buildLivesSlider(settings),
                
                const SizedBox(height: 20),
                
                // Difficulty Selection
                _buildSectionHeader('Difficulty'),
                _buildDifficultySlider(settings),
                
                const SizedBox(height: 30),
                
                // Gesture Training Section
                _buildGestureTrainingSection(),
                
                const SizedBox(height: 30),
                
                // Save Button
                _buildSaveButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Genos',
        ),
      ),
    );
  }

  Widget _buildImageSelector(SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: settings.selectedImage,
        hint: const Text(
          'Select Background Image',
          style: TextStyle(color: Colors.white70),
        ),
        items: settings.bgImages.map((String image) {
          return DropdownMenuItem<String>(
            value: image,
            child: Text(
              image,
              style: const TextStyle(color: Colors.black),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          settings.setSelectedImage(newValue);
        },
        dropdownColor: Colors.white,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: Container(),
        isExpanded: true,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildMusicSelector(SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: settings.selectedMusic,
        hint: const Text(
          'Select Background Music',
          style: TextStyle(color: Colors.white70),
        ),
        items: settings.bgMusic.map((String music) {
          return DropdownMenuItem<String>(
            value: music,
            child: Text(
              music,
              style: const TextStyle(color: Colors.black),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          settings.setSelectedMusic(newValue);
          // Apply music change immediately
          final gameProvider = Provider.of<GameStateProvider>(context, listen: false);
          gameProvider.applySettings(bgMusic: newValue);
        },
        dropdownColor: Colors.white,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: Container(),
        isExpanded: true,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildVolumeSlider(SettingsProvider settings) {
    return Column(
      children: [
        Slider(
          value: settings.volumeLevel.toDouble(),
          min: 0,
          max: 100,
          divisions: 100,
          activeColor: const Color(0xFF4D9DE0),
          inactiveColor: Colors.white.withValues(alpha: 0.3),
          onChanged: (double value) {
            settings.setVolumeLevel(value.toInt());
            // Apply volume change immediately to background music
            final gameProvider = Provider.of<GameStateProvider>(context, listen: false);
            gameProvider.applySettings(volume: value.toInt());
          },
        ),
        Text(
          'Volume: ${settings.volumeLevel}%',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Genos',
          ),
        ),
      ],
    );
  }

  Widget _buildCountdownSoundSelector(SettingsProvider settings) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: settings.selectedSineshifterSound,
        items: settings.sineshifterSounds.map((String sound) {
          return DropdownMenuItem<String>(
            value: sound,
            child: Text(
              sound,
              style: const TextStyle(color: Colors.black),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (newValue != null) {
            settings.setSelectedSineshifterSound(newValue);
          }
        },
        dropdownColor: Colors.white,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: Container(),
        isExpanded: true,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildLivesSlider(SettingsProvider settings) {
    return Column(
      children: [
        Slider(
          value: settings.selectedLives.toDouble(),
          min: 1,
          max: 10,
          divisions: 9,
          activeColor: const Color(0xFF4D9DE0),
          inactiveColor: Colors.white.withValues(alpha: 0.3),
          onChanged: (double value) {
            settings.setSelectedLives(value.toInt());
          },
        ),
        Text(
          'Lives: ${settings.selectedLives}',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Genos',
          ),
        ),
      ],
    );
  }

  Widget _buildDifficultySlider(SettingsProvider settings) {
    final difficulties = ['light', 'medium', 'hard'];
    final difficultyLabels = ['Light', 'Medium', 'Hard'];
    final currentIndex = difficulties.indexOf(settings.selectedDifficulty);
    
    return Column(
      children: [
        Slider(
          value: currentIndex.toDouble(),
          min: 0,
          max: 2,
          divisions: 2,
          activeColor: const Color(0xFF4D9DE0),
          inactiveColor: Colors.white.withValues(alpha: 0.3),
          onChanged: (double value) {
            settings.setSelectedDifficulty(difficulties[value.toInt()]);
          },
        ),
        Text(
          'Difficulty: ${difficultyLabels[currentIndex]}',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Genos',
          ),
        ),
      ],
    );
  }

  Widget _buildGestureTrainingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Gesture Training'),
        const Text(
          'Practice your rock, paper, and scissors gestures',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white70,
            fontFamily: 'Genos',
          ),
        ),
        const SizedBox(height: 10),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const GestureTrainingScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF9B59B6).withValues(alpha: 0.9),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Start Gesture Training',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Genos',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 60,
      child: ElevatedButton(
        onPressed: () async {
          final settings = Provider.of<SettingsProvider>(context, listen: false);
          
          // Save settings to SettingsProvider storage
          await settings.saveSettings();
          debugPrint('💾 RETURN: SettingsProvider settings saved');
          
          // Apply all settings to game (this will also auto-save via GameStateProvider)
          final gameProvider = Provider.of<GameStateProvider>(context, listen: false);
          gameProvider.applySettings(
            lives: settings.selectedLives,
            difficulty: settings.selectedDifficulty,
            bgMusic: settings.selectedMusic,
            bgImage: settings.selectedImage,
            sineshifterSound: settings.selectedSineshifterSound,
            volume: settings.volumeLevel,
            saveToStorage: true, // Ensure game settings are also saved
          );
          debugPrint('🎮 RETURN: GameStateProvider settings applied and saved');
          
          widget.onReturnToGame();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Return to Game',
          style: TextStyle(
            fontSize: 18,
            fontFamily: 'Genos',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}