#!/bin/bash

# Script de build optimisé pour Rock Paper Scissors Flutter
# Résout les problèmes de mémoire Gradle et AndroidX

echo "🚀 Build optimisé pour Rock Paper Scissors Flutter"
echo "=================================================="

# Vérifier si Flutter est installé
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter n'est pas installé ou pas dans le PATH"
    echo "💡 Installez Flutter: https://docs.flutter.dev/get-started/install"
    exit 1
fi

# Nettoyer le projet
echo "🧹 Nettoyage du projet..."
flutter clean

# Récupérer les dépendances
echo "📦 Récupération des dépendances..."
flutter pub get

# Optimisations pour éviter les problèmes de mémoire
echo "⚙️ Application des optimisations mémoire..."

# Créer un gradle.properties optimisé si nécessaire
if [ ! -f "android/gradle.properties" ]; then
    echo "📝 Création du fichier gradle.properties optimisé..."
    cat > android/gradle.properties << EOF
# Optimisation mémoire Gradle pour éviter le thrashing du GC
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:MaxGCPauseMillis=200
android.useAndroidX=true
android.enableJetifier=true

# Performance et optimisations
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.workers.max=2
android.enableD8.desugaring=true
android.enableR8.fullMode=false

# Optimisations spécifiques pour MediaPipe
android.enableBuildCache=false
org.gradle.caching=false
android.injected.testOnly=false
EOF
fi

# Build en mode debug avec gestion d'erreur
echo "🔨 Build en mode debug..."
if flutter build apk --debug --verbose; then
    echo "✅ Build debug réussi!"
    echo "📱 APK généré: build/app/outputs/flutter-apk/app-debug.apk"
else
    echo "❌ Échec du build debug"
    echo ""
    echo "🔧 Solutions possibles:"
    echo "1. Vérifiez que Java 11+ est installé"
    echo "2. Libérez de la mémoire système"
    echo "3. Fermez les autres applications"
    echo "4. Essayez: flutter build apk --debug --no-tree-shake-icons"
    exit 1
fi

echo ""
echo "🎉 Build terminé avec succès!"
echo "💡 Pour installer sur un appareil: flutter install"
